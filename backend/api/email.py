"""
邮件管理API路由
处理邮件的CRUD操作、分析和处理
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional
import logging
import uuid

from core.database import get_db
from core.security import verify_token
from models.email import Email, EmailAttachment
from schemas.email import (
    EmailCreate,
    EmailUpdate,
    EmailResponse,
    EmailListResponse,
    EmailAnalysisResponse
)

logger = logging.getLogger(__name__)
router = APIRouter()

async def get_current_user_id(token: str = Depends(verify_token)) -> uuid.UUID:
    """获取当前用户ID"""
    payload = verify_token(token)
    user_id = payload.get("sub")
    return uuid.UUID(user_id)

@router.get("/", response_model=EmailListResponse, summary="获取邮件列表")
async def get_emails(
    skip: int = Query(0, ge=0, description="跳过的记录数"),
    limit: int = Query(100, ge=1, le=1000, description="返回的记录数"),
    status_filter: Optional[str] = Query(None, description="状态过滤"),
    folder: Optional[str] = Query(None, description="文件夹过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """获取邮件列表"""
    try:
        query = db.query(Email).filter(Email.user_id == current_user_id)
        
        # 应用过滤条件
        if status_filter:
            query = query.filter(Email.status == status_filter)
        if folder:
            query = query.filter(Email.folder == folder)
        if search:
            query = query.filter(
                Email.subject.contains(search) | 
                Email.content.contains(search) |
                Email.sender_email.contains(search)
            )
        
        # 获取总数
        total = query.count()
        
        # 分页查询
        emails = query.offset(skip).limit(limit).all()
        
        return EmailListResponse(
            emails=[EmailResponse.from_orm(email) for email in emails],
            total=total,
            skip=skip,
            limit=limit
        )
        
    except Exception as e:
        logger.error(f"获取邮件列表失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取邮件列表失败"
        )

@router.get("/{email_id}", response_model=EmailResponse, summary="获取邮件详情")
async def get_email(
    email_id: str,
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """获取邮件详情"""
    try:
        email = db.query(Email).filter(
            Email.id == uuid.UUID(email_id),
            Email.user_id == current_user_id
        ).first()
        
        if not email:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="邮件不存在"
            )
        
        return EmailResponse.from_orm(email)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"获取邮件详情失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取邮件详情失败"
        )

@router.post("/", response_model=EmailResponse, status_code=status.HTTP_201_CREATED, summary="创建邮件")
async def create_email(
    email_data: EmailCreate,
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """创建邮件"""
    try:
        new_email = Email(
            user_id=current_user_id,
            **email_data.dict()
        )
        
        db.add(new_email)
        db.commit()
        db.refresh(new_email)
        
        logger.info(f"邮件创建成功: {new_email.id}")
        
        return EmailResponse.from_orm(new_email)
        
    except Exception as e:
        logger.error(f"邮件创建失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="邮件创建失败"
        )

@router.put("/{email_id}", response_model=EmailResponse, summary="更新邮件")
async def update_email(
    email_id: str,
    email_data: EmailUpdate,
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """更新邮件"""
    try:
        email = db.query(Email).filter(
            Email.id == uuid.UUID(email_id),
            Email.user_id == current_user_id
        ).first()
        
        if not email:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="邮件不存在"
            )
        
        # 更新字段
        update_data = email_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(email, field, value)
        
        db.commit()
        db.refresh(email)
        
        logger.info(f"邮件更新成功: {email.id}")
        
        return EmailResponse.from_orm(email)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"邮件更新失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="邮件更新失败"
        )

@router.delete("/{email_id}", summary="删除邮件")
async def delete_email(
    email_id: str,
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """删除邮件"""
    try:
        email = db.query(Email).filter(
            Email.id == uuid.UUID(email_id),
            Email.user_id == current_user_id
        ).first()
        
        if not email:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="邮件不存在"
            )
        
        db.delete(email)
        db.commit()
        
        logger.info(f"邮件删除成功: {email_id}")
        
        return {"message": "邮件删除成功"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"邮件删除失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="邮件删除失败"
        )

@router.post("/{email_id}/analyze", response_model=EmailAnalysisResponse, summary="分析邮件")
async def analyze_email(
    email_id: str,
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """分析邮件内容"""
    try:
        email = db.query(Email).filter(
            Email.id == uuid.UUID(email_id),
            Email.user_id == current_user_id
        ).first()
        
        if not email:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="邮件不存在"
            )
        
        # TODO: 集成AI分析服务
        analysis_result = {
            "sentiment": "neutral",
            "category": "general",
            "priority": "medium",
            "suggested_actions": ["reply", "archive"],
            "summary": "邮件内容摘要",
            "keywords": ["关键词1", "关键词2"]
        }
        
        # 更新邮件分析结果
        email.analysis_result = analysis_result
        email.status = "analyzed"
        
        db.commit()
        
        logger.info(f"邮件分析完成: {email_id}")
        
        return EmailAnalysisResponse(**analysis_result)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"邮件分析失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="邮件分析失败"
        )

@router.post("/{email_id}/process", summary="处理邮件")
async def process_email(
    email_id: str,
    action: str = Query(..., description="处理动作"),
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """处理邮件（回复、转发、归档等）"""
    try:
        email = db.query(Email).filter(
            Email.id == uuid.UUID(email_id),
            Email.user_id == current_user_id
        ).first()
        
        if not email:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="邮件不存在"
            )
        
        # TODO: 实现具体的邮件处理逻辑
        if action == "reply":
            # 自动回复逻辑
            pass
        elif action == "forward":
            # 转发逻辑
            pass
        elif action == "archive":
            # 归档逻辑
            email.folder = "archived"
        elif action == "delete":
            # 删除逻辑
            email.folder = "trash"
        
        email.status = "processed"
        db.commit()
        
        logger.info(f"邮件处理完成: {email_id}, 动作: {action}")
        
        return {"message": f"邮件{action}处理完成"}
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"邮件处理失败: {e}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="邮件处理失败"
        )
