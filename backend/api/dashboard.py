"""
仪表板API路由
提供系统概览、统计数据和监控信息
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from sqlalchemy import func, desc
from typing import List, Optional, Dict, Any
from datetime import datetime, timedelta
import logging
import uuid

from core.database import get_db
from core.security import verify_token
from models.user import User
from models.email import Email
from models.agent import Agent, AgentExecution
from models.task import Task, TaskExecution
from models.mcp import MCPService, MCPServiceCall

logger = logging.getLogger(__name__)
router = APIRouter()

async def get_current_user_id(token: str = Depends(verify_token)) -> uuid.UUID:
    """获取当前用户ID"""
    payload = verify_token(token)
    user_id = payload.get("sub")
    return uuid.UUID(user_id)

@router.get("/overview", summary="获取系统概览")
async def get_system_overview(
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """获取系统概览数据"""
    try:
        # 邮件统计
        email_stats = {
            "total": db.query(Email).filter(Email.user_id == current_user_id).count(),
            "unread": db.query(Email).filter(
                Email.user_id == current_user_id,
                Email.status == "unread"
            ).count(),
            "today": db.query(Email).filter(
                Email.user_id == current_user_id,
                func.date(Email.created_at) == datetime.now().date()
            ).count()
        }
        
        # Agent统计
        agent_stats = {
            "total": db.query(Agent).filter(Agent.user_id == current_user_id).count(),
            "active": db.query(Agent).filter(
                Agent.user_id == current_user_id,
                Agent.is_active == True
            ).count(),
            "executions_today": db.query(AgentExecution).filter(
                AgentExecution.user_id == current_user_id,
                func.date(AgentExecution.created_at) == datetime.now().date()
            ).count()
        }
        
        # 任务统计
        task_stats = {
            "total": db.query(Task).filter(Task.user_id == current_user_id).count(),
            "active": db.query(Task).filter(
                Task.user_id == current_user_id,
                Task.is_active == True
            ).count(),
            "scheduled": db.query(Task).filter(
                Task.user_id == current_user_id,
                Task.is_scheduled == True
            ).count()
        }
        
        # MCP服务统计
        mcp_stats = {
            "total": db.query(MCPService).filter(MCPService.user_id == current_user_id).count(),
            "active": db.query(MCPService).filter(
                MCPService.user_id == current_user_id,
                MCPService.is_active == True
            ).count(),
            "calls_today": db.query(MCPServiceCall).filter(
                MCPServiceCall.user_id == current_user_id,
                func.date(MCPServiceCall.created_at) == datetime.now().date()
            ).count()
        }
        
        return {
            "email": email_stats,
            "agents": agent_stats,
            "tasks": task_stats,
            "mcp_services": mcp_stats,
            "last_updated": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取系统概览失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统概览失败"
        )

@router.get("/activity", summary="获取活动记录")
async def get_activity_feed(
    limit: int = Query(50, ge=1, le=200, description="返回的记录数"),
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """获取用户活动记录"""
    try:
        activities = []
        
        # 最近的Agent执行
        recent_agent_executions = db.query(AgentExecution).filter(
            AgentExecution.user_id == current_user_id
        ).order_by(desc(AgentExecution.created_at)).limit(10).all()
        
        for execution in recent_agent_executions:
            activities.append({
                "type": "agent_execution",
                "title": f"Agent执行: {execution.agent_id}",
                "description": f"状态: {execution.status}",
                "timestamp": execution.created_at.isoformat(),
                "status": execution.status,
                "metadata": {
                    "agent_id": str(execution.agent_id),
                    "execution_id": str(execution.id)
                }
            })
        
        # 最近的任务执行
        recent_task_executions = db.query(TaskExecution).filter(
            TaskExecution.user_id == current_user_id
        ).order_by(desc(TaskExecution.created_at)).limit(10).all()
        
        for execution in recent_task_executions:
            activities.append({
                "type": "task_execution",
                "title": f"任务执行: {execution.task_id}",
                "description": f"状态: {execution.status}",
                "timestamp": execution.created_at.isoformat(),
                "status": execution.status,
                "metadata": {
                    "task_id": str(execution.task_id),
                    "execution_id": str(execution.id)
                }
            })
        
        # 最近的MCP服务调用
        recent_mcp_calls = db.query(MCPServiceCall).filter(
            MCPServiceCall.user_id == current_user_id
        ).order_by(desc(MCPServiceCall.created_at)).limit(10).all()
        
        for call in recent_mcp_calls:
            activities.append({
                "type": "mcp_call",
                "title": f"MCP调用: {call.method}",
                "description": f"状态: {call.status}",
                "timestamp": call.created_at.isoformat(),
                "status": call.status,
                "metadata": {
                    "service_id": str(call.service_id),
                    "call_id": str(call.id),
                    "method": call.method
                }
            })
        
        # 按时间排序并限制数量
        activities.sort(key=lambda x: x["timestamp"], reverse=True)
        activities = activities[:limit]
        
        return {
            "activities": activities,
            "total": len(activities),
            "last_updated": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取活动记录失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取活动记录失败"
        )

@router.get("/performance", summary="获取性能指标")
async def get_performance_metrics(
    days: int = Query(7, ge=1, le=30, description="统计天数"),
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """获取性能指标数据"""
    try:
        start_date = datetime.now() - timedelta(days=days)
        
        # Agent执行性能
        agent_executions = db.query(AgentExecution).filter(
            AgentExecution.user_id == current_user_id,
            AgentExecution.created_at >= start_date
        ).all()
        
        agent_metrics = {
            "total_executions": len(agent_executions),
            "success_rate": 0,
            "avg_execution_time": 0,
            "daily_counts": {}
        }
        
        if agent_executions:
            successful = len([e for e in agent_executions if e.status == "completed"])
            agent_metrics["success_rate"] = (successful / len(agent_executions)) * 100
            
            # 计算平均执行时间（模拟数据）
            agent_metrics["avg_execution_time"] = 2.5
            
            # 按日统计
            for execution in agent_executions:
                date_key = execution.created_at.date().isoformat()
                agent_metrics["daily_counts"][date_key] = agent_metrics["daily_counts"].get(date_key, 0) + 1
        
        # 任务执行性能
        task_executions = db.query(TaskExecution).filter(
            TaskExecution.user_id == current_user_id,
            TaskExecution.created_at >= start_date
        ).all()
        
        task_metrics = {
            "total_executions": len(task_executions),
            "success_rate": 0,
            "avg_execution_time": 0,
            "daily_counts": {}
        }
        
        if task_executions:
            successful = len([e for e in task_executions if e.status == "completed"])
            task_metrics["success_rate"] = (successful / len(task_executions)) * 100
            
            # 计算平均执行时间（模拟数据）
            task_metrics["avg_execution_time"] = 1.8
            
            # 按日统计
            for execution in task_executions:
                date_key = execution.created_at.date().isoformat()
                task_metrics["daily_counts"][date_key] = task_metrics["daily_counts"].get(date_key, 0) + 1
        
        # MCP服务调用性能
        mcp_calls = db.query(MCPServiceCall).filter(
            MCPServiceCall.user_id == current_user_id,
            MCPServiceCall.created_at >= start_date
        ).all()
        
        mcp_metrics = {
            "total_calls": len(mcp_calls),
            "success_rate": 0,
            "avg_response_time": 0,
            "daily_counts": {}
        }
        
        if mcp_calls:
            successful = len([c for c in mcp_calls if c.status == "completed"])
            mcp_metrics["success_rate"] = (successful / len(mcp_calls)) * 100
            
            # 计算平均响应时间（模拟数据）
            mcp_metrics["avg_response_time"] = 0.8
            
            # 按日统计
            for call in mcp_calls:
                date_key = call.created_at.date().isoformat()
                mcp_metrics["daily_counts"][date_key] = mcp_metrics["daily_counts"].get(date_key, 0) + 1
        
        return {
            "period": {
                "start_date": start_date.isoformat(),
                "end_date": datetime.now().isoformat(),
                "days": days
            },
            "agents": agent_metrics,
            "tasks": task_metrics,
            "mcp_services": mcp_metrics,
            "last_updated": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取性能指标失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取性能指标失败"
        )

@router.get("/health", summary="获取系统健康状态")
async def get_system_health(
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """获取系统健康状态"""
    try:
        health_status = {
            "overall": "healthy",
            "components": {},
            "last_check": datetime.now().isoformat()
        }
        
        # 检查数据库连接
        try:
            db.execute("SELECT 1")
            health_status["components"]["database"] = {
                "status": "healthy",
                "message": "数据库连接正常"
            }
        except Exception as e:
            health_status["components"]["database"] = {
                "status": "unhealthy",
                "message": f"数据库连接异常: {str(e)}"
            }
            health_status["overall"] = "unhealthy"
        
        # 检查活跃的MCP服务
        active_mcp_services = db.query(MCPService).filter(
            MCPService.user_id == current_user_id,
            MCPService.is_active == True
        ).count()
        
        health_status["components"]["mcp_services"] = {
            "status": "healthy" if active_mcp_services > 0 else "warning",
            "message": f"活跃MCP服务: {active_mcp_services}个",
            "count": active_mcp_services
        }
        
        # 检查活跃的Agent
        active_agents = db.query(Agent).filter(
            Agent.user_id == current_user_id,
            Agent.is_active == True
        ).count()
        
        health_status["components"]["agents"] = {
            "status": "healthy" if active_agents > 0 else "warning",
            "message": f"活跃Agent: {active_agents}个",
            "count": active_agents
        }
        
        # 检查调度任务
        scheduled_tasks = db.query(Task).filter(
            Task.user_id == current_user_id,
            Task.is_scheduled == True,
            Task.is_active == True
        ).count()
        
        health_status["components"]["scheduled_tasks"] = {
            "status": "healthy",
            "message": f"调度任务: {scheduled_tasks}个",
            "count": scheduled_tasks
        }
        
        return health_status
        
    except Exception as e:
        logger.error(f"获取系统健康状态失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统健康状态失败"
        )

@router.get("/notifications", summary="获取系统通知")
async def get_notifications(
    unread_only: bool = Query(False, description="只返回未读通知"),
    limit: int = Query(20, ge=1, le=100, description="返回的记录数"),
    db: Session = Depends(get_db),
    current_user_id: uuid.UUID = Depends(get_current_user_id)
):
    """获取系统通知"""
    try:
        notifications = []
        
        # 检查失败的执行
        failed_executions = db.query(AgentExecution).filter(
            AgentExecution.user_id == current_user_id,
            AgentExecution.status == "failed",
            AgentExecution.created_at >= datetime.now() - timedelta(days=7)
        ).limit(5).all()
        
        for execution in failed_executions:
            notifications.append({
                "id": f"agent_failed_{execution.id}",
                "type": "error",
                "title": "Agent执行失败",
                "message": f"Agent {execution.agent_id} 执行失败",
                "timestamp": execution.created_at.isoformat(),
                "is_read": False,
                "metadata": {
                    "agent_id": str(execution.agent_id),
                    "execution_id": str(execution.id)
                }
            })
        
        # 检查失败的任务
        failed_tasks = db.query(TaskExecution).filter(
            TaskExecution.user_id == current_user_id,
            TaskExecution.status == "failed",
            TaskExecution.created_at >= datetime.now() - timedelta(days=7)
        ).limit(5).all()
        
        for execution in failed_tasks:
            notifications.append({
                "id": f"task_failed_{execution.id}",
                "type": "error",
                "title": "任务执行失败",
                "message": f"任务 {execution.task_id} 执行失败",
                "timestamp": execution.created_at.isoformat(),
                "is_read": False,
                "metadata": {
                    "task_id": str(execution.task_id),
                    "execution_id": str(execution.id)
                }
            })
        
        # 按时间排序
        notifications.sort(key=lambda x: x["timestamp"], reverse=True)
        
        # 应用过滤和限制
        if unread_only:
            notifications = [n for n in notifications if not n["is_read"]]
        
        notifications = notifications[:limit]
        
        return {
            "notifications": notifications,
            "total": len(notifications),
            "unread_count": len([n for n in notifications if not n["is_read"]]),
            "last_updated": datetime.now().isoformat()
        }
        
    except Exception as e:
        logger.error(f"获取系统通知失败: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="获取系统通知失败"
        )
