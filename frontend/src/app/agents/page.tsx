'use client';

import React, { useState, useEffect } from 'react';
import { 
  CpuChipIcon,
  PlayIcon,
  PauseIcon,
  StopIcon,
  Cog6ToothIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from '@heroicons/react/24/outline';

interface Agent {
  id: string;
  name: string;
  description: string;
  status: 'running' | 'stopped' | 'error' | 'paused';
  type: 'email_processor' | 'ai_analyzer' | 'task_executor' | 'mcp_client';
  config: {
    enabled: boolean;
    auto_start: boolean;
    max_concurrent_tasks: number;
    timeout_seconds: number;
  };
  stats: {
    tasks_processed: number;
    success_rate: number;
    avg_processing_time: number;
    last_activity: string;
  };
  created_at: string;
  updated_at: string;
}

const AgentsPage: React.FC = () => {
  const [agents, setAgents] = useState<Agent[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedAgent, setSelectedAgent] = useState<Agent | null>(null);
  const [showConfig, setShowConfig] = useState(false);

  useEffect(() => {
    fetchAgents();
  }, []);

  const fetchAgents = async () => {
    try {
      setLoading(true);
      // TODO: 替换为实际的API调用
      const response = await fetch('/api/agents');
      if (response.ok) {
        const data = await response.json();
        setAgents(data);
      }
    } catch (error) {
      console.error('获取Agent失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: Agent['status']) => {
    switch (status) {
      case 'running':
        return <PlayIcon className="h-5 w-5 text-green-500" />;
      case 'stopped':
        return <StopIcon className="h-5 w-5 text-gray-500" />;
      case 'paused':
        return <PauseIcon className="h-5 w-5 text-yellow-500" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <CpuChipIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: Agent['status']) => {
    switch (status) {
      case 'running': return '运行中';
      case 'stopped': return '已停止';
      case 'paused': return '已暂停';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  const getStatusColor = (status: Agent['status']) => {
    switch (status) {
      case 'running': return 'bg-green-100 text-green-800';
      case 'stopped': return 'bg-gray-100 text-gray-800';
      case 'paused': return 'bg-yellow-100 text-yellow-800';
      case 'error': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeText = (type: Agent['type']) => {
    switch (type) {
      case 'email_processor': return '邮件处理器';
      case 'ai_analyzer': return 'AI分析器';
      case 'task_executor': return '任务执行器';
      case 'mcp_client': return 'MCP客户端';
      default: return '未知类型';
    }
  };

  const handleAgentAction = async (agentId: string, action: 'start' | 'stop' | 'pause' | 'restart') => {
    try {
      const response = await fetch(`/api/agents/${agentId}/${action}`, {
        method: 'POST',
      });
      if (response.ok) {
        await fetchAgents();
      }
    } catch (error) {
      console.error(`执行Agent操作失败:`, error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">AI Agent 管理</h1>
        <p className="text-gray-600">
          管理和监控系统中的AI Agent，包括邮件处理器、分析器和任务执行器
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Agent列表 */}
        <div className="lg:col-span-2 space-y-4">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-xl font-semibold text-gray-800">
              Agent 列表 ({agents.length})
            </h2>
            <button className="px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600">
              创建新Agent
            </button>
          </div>
          
          {agents.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无Agent
            </div>
          ) : (
            agents.map((agent) => (
              <div
                key={agent.id}
                onClick={() => setSelectedAgent(agent)}
                className={`p-6 border rounded-lg cursor-pointer transition-colors ${
                  selectedAgent?.id === agent.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    {getStatusIcon(agent.status)}
                    <div>
                      <h3 className="font-medium text-gray-900">{agent.name}</h3>
                      <p className="text-sm text-gray-600">{getTypeText(agent.type)}</p>
                    </div>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(agent.status)}`}>
                    {getStatusText(agent.status)}
                  </span>
                </div>
                
                <p className="text-sm text-gray-700 mb-4">{agent.description}</p>
                
                <div className="grid grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-500">处理任务:</span>
                    <p className="font-medium">{agent.stats.tasks_processed}</p>
                  </div>
                  <div>
                    <span className="text-gray-500">成功率:</span>
                    <p className="font-medium">{(agent.stats.success_rate * 100).toFixed(1)}%</p>
                  </div>
                  <div>
                    <span className="text-gray-500">平均耗时:</span>
                    <p className="font-medium">{agent.stats.avg_processing_time}s</p>
                  </div>
                </div>
                
                <div className="flex items-center justify-between mt-4">
                  <span className="text-xs text-gray-500">
                    最后活动: {new Date(agent.stats.last_activity).toLocaleString('zh-CN')}
                  </span>
                  <div className="flex space-x-2">
                    {agent.status === 'stopped' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAgentAction(agent.id, 'start');
                        }}
                        className="p-1 text-green-600 hover:bg-green-100 rounded"
                        title="启动"
                      >
                        <PlayIcon className="h-4 w-4" />
                      </button>
                    )}
                    {agent.status === 'running' && (
                      <>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAgentAction(agent.id, 'pause');
                          }}
                          className="p-1 text-yellow-600 hover:bg-yellow-100 rounded"
                          title="暂停"
                        >
                          <PauseIcon className="h-4 w-4" />
                        </button>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            handleAgentAction(agent.id, 'stop');
                          }}
                          className="p-1 text-red-600 hover:bg-red-100 rounded"
                          title="停止"
                        >
                          <StopIcon className="h-4 w-4" />
                        </button>
                      </>
                    )}
                    {agent.status === 'paused' && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAgentAction(agent.id, 'start');
                        }}
                        className="p-1 text-green-600 hover:bg-green-100 rounded"
                        title="恢复"
                      >
                        <PlayIcon className="h-4 w-4" />
                      </button>
                    )}
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setShowConfig(true);
                      }}
                      className="p-1 text-gray-600 hover:bg-gray-100 rounded"
                      title="配置"
                    >
                      <Cog6ToothIcon className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            ))
          )}
        </div>

        {/* Agent详情 */}
        <div className="bg-white border rounded-lg p-6">
          {selectedAgent ? (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  Agent 详情
                </h2>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(selectedAgent.status)}
                  <span className="text-sm text-gray-600">
                    {getStatusText(selectedAgent.status)}
                  </span>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    名称
                  </label>
                  <p className="text-gray-900">{selectedAgent.name}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    类型
                  </label>
                  <p className="text-gray-900">{getTypeText(selectedAgent.type)}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    描述
                  </label>
                  <p className="text-gray-900">{selectedAgent.description}</p>
                </div>
                
                <div className="border-t pt-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                    <ChartBarIcon className="h-5 w-5 mr-2" />
                    性能统计
                  </h3>
                  
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-gray-500">处理任务数:</span>
                      <p className="font-medium text-lg">{selectedAgent.stats.tasks_processed}</p>
                    </div>
                    <div>
                      <span className="text-gray-500">成功率:</span>
                      <p className="font-medium text-lg">
                        {(selectedAgent.stats.success_rate * 100).toFixed(1)}%
                      </p>
                    </div>
                    <div>
                      <span className="text-gray-500">平均处理时间:</span>
                      <p className="font-medium text-lg">{selectedAgent.stats.avg_processing_time}s</p>
                    </div>
                    <div>
                      <span className="text-gray-500">最后活动:</span>
                      <p className="font-medium text-sm">
                        {new Date(selectedAgent.stats.last_activity).toLocaleString('zh-CN')}
                      </p>
                    </div>
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-3 flex items-center">
                    <Cog6ToothIcon className="h-5 w-5 mr-2" />
                    配置信息
                  </h3>
                  
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">启用状态:</span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        selectedAgent.config.enabled 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {selectedAgent.config.enabled ? '已启用' : '已禁用'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">自动启动:</span>
                      <span className={`px-2 py-1 rounded-full text-xs ${
                        selectedAgent.config.auto_start 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {selectedAgent.config.auto_start ? '是' : '否'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">最大并发任务:</span>
                      <span className="font-medium">{selectedAgent.config.max_concurrent_tasks}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-500">超时时间:</span>
                      <span className="font-medium">{selectedAgent.config.timeout_seconds}s</span>
                    </div>
                  </div>
                </div>
                
                <div className="border-t pt-4">
                  <div className="text-xs text-gray-500 space-y-1">
                    <p>创建时间: {new Date(selectedAgent.created_at).toLocaleString('zh-CN')}</p>
                    <p>更新时间: {new Date(selectedAgent.updated_at).toLocaleString('zh-CN')}</p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              请选择一个Agent查看详情
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default AgentsPage;
