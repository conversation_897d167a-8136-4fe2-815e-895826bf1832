'use client';

import React, { useState, useEffect } from 'react';
import { 
  ClockIcon,
  CheckCircleIcon,
  ExclamationTriangleIcon,
  PlayIcon,
  PauseIcon,
  ArrowPathIcon,
  TrashIcon,
  FunnelIcon
} from '@heroicons/react/24/outline';

interface Task {
  id: string;
  name: string;
  description: string;
  type: 'email_processing' | 'ai_analysis' | 'mcp_request' | 'file_processing';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  priority: 'low' | 'medium' | 'high';
  progress: number;
  agent_id?: string;
  agent_name?: string;
  input_data: any;
  output_data?: any;
  error_message?: string;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  estimated_duration?: number;
  actual_duration?: number;
}

const TasksPage: React.FC = () => {
  const [tasks, setTasks] = useState<Task[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [filter, setFilter] = useState<{
    status: string;
    type: string;
    priority: string;
  }>({
    status: 'all',
    type: 'all',
    priority: 'all'
  });

  useEffect(() => {
    fetchTasks();
    const interval = setInterval(fetchTasks, 5000); // 每5秒刷新一次
    return () => clearInterval(interval);
  }, []);

  const fetchTasks = async () => {
    try {
      setLoading(true);
      // TODO: 替换为实际的API调用
      const response = await fetch('/api/tasks');
      if (response.ok) {
        const data = await response.json();
        setTasks(data);
      }
    } catch (error) {
      console.error('获取任务失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: Task['status']) => {
    switch (status) {
      case 'pending':
        return <ClockIcon className="h-5 w-5 text-gray-500" />;
      case 'running':
        return <PlayIcon className="h-5 w-5 text-blue-500" />;
      case 'completed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'failed':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      case 'cancelled':
        return <PauseIcon className="h-5 w-5 text-yellow-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: Task['status']) => {
    switch (status) {
      case 'pending': return '等待中';
      case 'running': return '执行中';
      case 'completed': return '已完成';
      case 'failed': return '失败';
      case 'cancelled': return '已取消';
      default: return '未知';
    }
  };

  const getStatusColor = (status: Task['status']) => {
    switch (status) {
      case 'pending': return 'bg-gray-100 text-gray-800';
      case 'running': return 'bg-blue-100 text-blue-800';
      case 'completed': return 'bg-green-100 text-green-800';
      case 'failed': return 'bg-red-100 text-red-800';
      case 'cancelled': return 'bg-yellow-100 text-yellow-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: Task['priority']) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getTypeText = (type: Task['type']) => {
    switch (type) {
      case 'email_processing': return '邮件处理';
      case 'ai_analysis': return 'AI分析';
      case 'mcp_request': return 'MCP请求';
      case 'file_processing': return '文件处理';
      default: return '未知类型';
    }
  };

  const handleTaskAction = async (taskId: string, action: 'cancel' | 'retry' | 'delete') => {
    try {
      const response = await fetch(`/api/tasks/${taskId}/${action}`, {
        method: 'POST',
      });
      if (response.ok) {
        await fetchTasks();
      }
    } catch (error) {
      console.error(`执行任务操作失败:`, error);
    }
  };

  const filteredTasks = tasks.filter(task => {
    if (filter.status !== 'all' && task.status !== filter.status) return false;
    if (filter.type !== 'all' && task.type !== filter.type) return false;
    if (filter.priority !== 'all' && task.priority !== filter.priority) return false;
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">任务管理</h1>
        <p className="text-gray-600">
          监控和管理系统中的所有任务，包括邮件处理、AI分析和文件处理任务
        </p>
      </div>

      {/* 过滤器 */}
      <div className="bg-white border rounded-lg p-4 mb-6">
        <div className="flex items-center space-x-4">
          <FunnelIcon className="h-5 w-5 text-gray-500" />
          <span className="text-sm font-medium text-gray-700">过滤器:</span>
          
          <select
            value={filter.status}
            onChange={(e) => setFilter({...filter, status: e.target.value})}
            className="border border-gray-300 rounded px-3 py-1 text-sm"
          >
            <option value="all">全部状态</option>
            <option value="pending">等待中</option>
            <option value="running">执行中</option>
            <option value="completed">已完成</option>
            <option value="failed">失败</option>
            <option value="cancelled">已取消</option>
          </select>
          
          <select
            value={filter.type}
            onChange={(e) => setFilter({...filter, type: e.target.value})}
            className="border border-gray-300 rounded px-3 py-1 text-sm"
          >
            <option value="all">全部类型</option>
            <option value="email_processing">邮件处理</option>
            <option value="ai_analysis">AI分析</option>
            <option value="mcp_request">MCP请求</option>
            <option value="file_processing">文件处理</option>
          </select>
          
          <select
            value={filter.priority}
            onChange={(e) => setFilter({...filter, priority: e.target.value})}
            className="border border-gray-300 rounded px-3 py-1 text-sm"
          >
            <option value="all">全部优先级</option>
            <option value="high">高优先级</option>
            <option value="medium">中优先级</option>
            <option value="low">低优先级</option>
          </select>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 任务列表 */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            任务列表 ({filteredTasks.length})
          </h2>
          
          {filteredTasks.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无任务
            </div>
          ) : (
            filteredTasks.map((task) => (
              <div
                key={task.id}
                onClick={() => setSelectedTask(task)}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedTask?.id === task.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(task.status)}
                    <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(task.status)}`}>
                      {getStatusText(task.status)}
                    </span>
                  </div>
                  <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(task.priority)}`}>
                    {task.priority === 'high' ? '高优先级' : 
                     task.priority === 'medium' ? '中优先级' : '低优先级'}
                  </span>
                </div>
                
                <h3 className="font-medium text-gray-900 mb-1">{task.name}</h3>
                <p className="text-sm text-gray-600 mb-2">{getTypeText(task.type)}</p>
                
                {task.status === 'running' && (
                  <div className="mb-2">
                    <div className="flex justify-between text-xs text-gray-500 mb-1">
                      <span>进度</span>
                      <span>{task.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${task.progress}%` }}
                      ></div>
                    </div>
                  </div>
                )}
                
                <div className="flex items-center justify-between text-xs text-gray-500">
                  <span>
                    创建: {new Date(task.created_at).toLocaleString('zh-CN')}
                  </span>
                  {task.agent_name && (
                    <span>Agent: {task.agent_name}</span>
                  )}
                </div>
                
                <div className="flex justify-end space-x-1 mt-2">
                  {task.status === 'running' && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTaskAction(task.id, 'cancel');
                      }}
                      className="p-1 text-red-600 hover:bg-red-100 rounded"
                      title="取消任务"
                    >
                      <PauseIcon className="h-4 w-4" />
                    </button>
                  )}
                  {task.status === 'failed' && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTaskAction(task.id, 'retry');
                      }}
                      className="p-1 text-blue-600 hover:bg-blue-100 rounded"
                      title="重试任务"
                    >
                      <ArrowPathIcon className="h-4 w-4" />
                    </button>
                  )}
                  {(task.status === 'completed' || task.status === 'failed' || task.status === 'cancelled') && (
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        handleTaskAction(task.id, 'delete');
                      }}
                      className="p-1 text-gray-600 hover:bg-gray-100 rounded"
                      title="删除任务"
                    >
                      <TrashIcon className="h-4 w-4" />
                    </button>
                  )}
                </div>
              </div>
            ))
          )}
        </div>

        {/* 任务详情 */}
        <div className="bg-white border rounded-lg p-6">
          {selectedTask ? (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  任务详情
                </h2>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(selectedTask.status)}
                  <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(selectedTask.status)}`}>
                    {getStatusText(selectedTask.status)}
                  </span>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    任务名称
                  </label>
                  <p className="text-gray-900">{selectedTask.name}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    任务类型
                  </label>
                  <p className="text-gray-900">{getTypeText(selectedTask.type)}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    描述
                  </label>
                  <p className="text-gray-900">{selectedTask.description}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    优先级
                  </label>
                  <span className={`inline-block px-2 py-1 text-xs rounded-full ${getPriorityColor(selectedTask.priority)}`}>
                    {selectedTask.priority === 'high' ? '高优先级' : 
                     selectedTask.priority === 'medium' ? '中优先级' : '低优先级'}
                  </span>
                </div>
                
                {selectedTask.agent_name && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      执行Agent
                    </label>
                    <p className="text-gray-900">{selectedTask.agent_name}</p>
                  </div>
                )}
                
                {selectedTask.status === 'running' && (
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      执行进度
                    </label>
                    <div className="flex items-center space-x-2">
                      <div className="flex-1 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                          style={{ width: `${selectedTask.progress}%` }}
                        ></div>
                      </div>
                      <span className="text-sm text-gray-600">{selectedTask.progress}%</span>
                    </div>
                  </div>
                )}
                
                <div className="border-t pt-4">
                  <h3 className="text-lg font-medium text-gray-900 mb-3">
                    时间信息
                  </h3>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-500">创建时间:</span>
                      <span>{new Date(selectedTask.created_at).toLocaleString('zh-CN')}</span>
                    </div>
                    {selectedTask.started_at && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">开始时间:</span>
                        <span>{new Date(selectedTask.started_at).toLocaleString('zh-CN')}</span>
                      </div>
                    )}
                    {selectedTask.completed_at && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">完成时间:</span>
                        <span>{new Date(selectedTask.completed_at).toLocaleString('zh-CN')}</span>
                      </div>
                    )}
                    {selectedTask.estimated_duration && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">预计耗时:</span>
                        <span>{selectedTask.estimated_duration}秒</span>
                      </div>
                    )}
                    {selectedTask.actual_duration && (
                      <div className="flex justify-between">
                        <span className="text-gray-500">实际耗时:</span>
                        <span>{selectedTask.actual_duration}秒</span>
                      </div>
                    )}
                  </div>
                </div>
                
                {selectedTask.error_message && (
                  <div className="border-t pt-4">
                    <label className="block text-sm font-medium text-red-700 mb-1">
                      错误信息
                    </label>
                    <div className="bg-red-50 border border-red-200 rounded p-3">
                      <p className="text-red-800 text-sm">{selectedTask.error_message}</p>
                    </div>
                  </div>
                )}
                
                {selectedTask.output_data && (
                  <div className="border-t pt-4">
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      输出结果
                    </label>
                    <div className="bg-gray-50 border rounded p-3">
                      <pre className="text-sm text-gray-800 whitespace-pre-wrap">
                        {JSON.stringify(selectedTask.output_data, null, 2)}
                      </pre>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              请选择一个任务查看详情
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TasksPage;
