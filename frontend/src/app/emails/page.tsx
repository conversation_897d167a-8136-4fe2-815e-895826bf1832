'use client';

import React, { useState, useEffect } from 'react';
import { 
  EnvelopeIcon, 
  EyeIcon, 
  TrashIcon, 
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon
} from '@heroicons/react/24/outline';

interface Email {
  id: string;
  subject: string;
  sender: string;
  content: string;
  status: 'unread' | 'read' | 'processed' | 'error';
  created_at: string;
  processed_at?: string;
  ai_analysis?: {
    summary: string;
    suggested_actions: string[];
    priority: 'low' | 'medium' | 'high';
  };
}

const EmailsPage: React.FC = () => {
  const [emails, setEmails] = useState<Email[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedEmail, setSelectedEmail] = useState<Email | null>(null);
  const [filter, setFilter] = useState<'all' | 'unread' | 'processed'>('all');

  useEffect(() => {
    fetchEmails();
  }, []);

  const fetchEmails = async () => {
    try {
      setLoading(true);
      // TODO: 替换为实际的API调用
      const response = await fetch('/api/emails');
      if (response.ok) {
        const data = await response.json();
        setEmails(data);
      }
    } catch (error) {
      console.error('获取邮件失败:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusIcon = (status: Email['status']) => {
    switch (status) {
      case 'unread':
        return <EnvelopeIcon className="h-5 w-5 text-blue-500" />;
      case 'read':
        return <EyeIcon className="h-5 w-5 text-gray-500" />;
      case 'processed':
        return <CheckCircleIcon className="h-5 w-5 text-green-500" />;
      case 'error':
        return <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />;
      default:
        return <ClockIcon className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStatusText = (status: Email['status']) => {
    switch (status) {
      case 'unread': return '未读';
      case 'read': return '已读';
      case 'processed': return '已处理';
      case 'error': return '错误';
      default: return '未知';
    }
  };

  const getPriorityColor = (priority?: 'low' | 'medium' | 'high') => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800';
      case 'medium': return 'bg-yellow-100 text-yellow-800';
      case 'low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const filteredEmails = emails.filter(email => {
    if (filter === 'all') return true;
    if (filter === 'unread') return email.status === 'unread';
    if (filter === 'processed') return email.status === 'processed';
    return true;
  });

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-900 mb-4">邮件管理</h1>
        
        {/* 过滤器 */}
        <div className="flex space-x-4 mb-6">
          <button
            onClick={() => setFilter('all')}
            className={`px-4 py-2 rounded-lg ${
              filter === 'all' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            全部邮件
          </button>
          <button
            onClick={() => setFilter('unread')}
            className={`px-4 py-2 rounded-lg ${
              filter === 'unread' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            未读邮件
          </button>
          <button
            onClick={() => setFilter('processed')}
            className={`px-4 py-2 rounded-lg ${
              filter === 'processed' 
                ? 'bg-blue-500 text-white' 
                : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
            }`}
          >
            已处理
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* 邮件列表 */}
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800 mb-4">
            邮件列表 ({filteredEmails.length})
          </h2>
          
          {filteredEmails.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              暂无邮件
            </div>
          ) : (
            filteredEmails.map((email) => (
              <div
                key={email.id}
                onClick={() => setSelectedEmail(email)}
                className={`p-4 border rounded-lg cursor-pointer transition-colors ${
                  selectedEmail?.id === email.id
                    ? 'border-blue-500 bg-blue-50'
                    : 'border-gray-200 hover:border-gray-300 hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start justify-between mb-2">
                  <div className="flex items-center space-x-2">
                    {getStatusIcon(email.status)}
                    <span className="text-sm text-gray-600">
                      {getStatusText(email.status)}
                    </span>
                  </div>
                  {email.ai_analysis?.priority && (
                    <span className={`px-2 py-1 text-xs rounded-full ${getPriorityColor(email.ai_analysis.priority)}`}>
                      {email.ai_analysis.priority === 'high' ? '高优先级' : 
                       email.ai_analysis.priority === 'medium' ? '中优先级' : '低优先级'}
                    </span>
                  )}
                </div>
                
                <h3 className="font-medium text-gray-900 mb-1 truncate">
                  {email.subject}
                </h3>
                <p className="text-sm text-gray-600 mb-2">
                  来自: {email.sender}
                </p>
                <p className="text-xs text-gray-500">
                  {new Date(email.created_at).toLocaleString('zh-CN')}
                </p>
                
                {email.ai_analysis?.summary && (
                  <p className="text-sm text-gray-700 mt-2 line-clamp-2">
                    AI摘要: {email.ai_analysis.summary}
                  </p>
                )}
              </div>
            ))
          )}
        </div>

        {/* 邮件详情 */}
        <div className="bg-white border rounded-lg p-6">
          {selectedEmail ? (
            <div>
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-semibold text-gray-900">
                  邮件详情
                </h2>
                <div className="flex items-center space-x-2">
                  {getStatusIcon(selectedEmail.status)}
                  <span className="text-sm text-gray-600">
                    {getStatusText(selectedEmail.status)}
                  </span>
                </div>
              </div>
              
              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    主题
                  </label>
                  <p className="text-gray-900">{selectedEmail.subject}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    发件人
                  </label>
                  <p className="text-gray-900">{selectedEmail.sender}</p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    接收时间
                  </label>
                  <p className="text-gray-900">
                    {new Date(selectedEmail.created_at).toLocaleString('zh-CN')}
                  </p>
                </div>
                
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    邮件内容
                  </label>
                  <div className="bg-gray-50 p-4 rounded-lg">
                    <p className="text-gray-900 whitespace-pre-wrap">
                      {selectedEmail.content}
                    </p>
                  </div>
                </div>
                
                {selectedEmail.ai_analysis && (
                  <div className="border-t pt-4">
                    <h3 className="text-lg font-medium text-gray-900 mb-3">
                      AI 分析结果
                    </h3>
                    
                    <div className="space-y-3">
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          摘要
                        </label>
                        <p className="text-gray-900">
                          {selectedEmail.ai_analysis.summary}
                        </p>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          优先级
                        </label>
                        <span className={`inline-block px-2 py-1 text-xs rounded-full ${getPriorityColor(selectedEmail.ai_analysis.priority)}`}>
                          {selectedEmail.ai_analysis.priority === 'high' ? '高优先级' : 
                           selectedEmail.ai_analysis.priority === 'medium' ? '中优先级' : '低优先级'}
                        </span>
                      </div>
                      
                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-1">
                          建议操作
                        </label>
                        <ul className="list-disc list-inside space-y-1">
                          {selectedEmail.ai_analysis.suggested_actions.map((action, index) => (
                            <li key={index} className="text-gray-900 text-sm">
                              {action}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </div>
                )}
              </div>
            </div>
          ) : (
            <div className="text-center py-8 text-gray-500">
              请选择一封邮件查看详情
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default EmailsPage;
