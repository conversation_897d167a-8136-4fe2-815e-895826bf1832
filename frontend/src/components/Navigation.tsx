'use client';

import React from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  HomeIcon,
  EnvelopeIcon,
  CpuChipIcon,
  ClipboardDocumentListIcon,
  ChartBarIcon,
  Cog6ToothIcon,
} from '@heroicons/react/24/outline';

interface NavigationItem {
  name: string;
  href: string;
  icon: React.ComponentType<{ className?: string }>;
  description: string;
}

const navigationItems: NavigationItem[] = [
  {
    name: '仪表板',
    href: '/',
    icon: HomeIcon,
    description: '系统概览和统计信息',
  },
  {
    name: '邮件管理',
    href: '/emails',
    icon: EnvelopeIcon,
    description: '查看和管理邮件',
  },
  {
    name: 'AI Agent',
    href: '/agents',
    icon: CpuChipIcon,
    description: '管理AI代理和处理器',
  },
  {
    name: '任务管理',
    href: '/tasks',
    icon: ClipboardDocumentListIcon,
    description: '监控任务执行状态',
  },
  {
    name: '系统监控',
    href: '/monitoring',
    icon: ChartBarIcon,
    description: '系统性能和健康状态',
  },
  {
    name: '系统设置',
    href: '/settings',
    icon: Cog6ToothIcon,
    description: '配置系统参数',
  },
];

const Navigation: React.FC = () => {
  const pathname = usePathname();

  return (
    <nav className="bg-white shadow-sm border-r border-gray-200 w-64 min-h-screen">
      <div className="p-6">
        <div className="flex items-center space-x-3 mb-8">
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <EnvelopeIcon className="h-5 w-5 text-white" />
          </div>
          <div>
            <h1 className="text-xl font-bold text-gray-900">Mailer AI</h1>
            <p className="text-xs text-gray-500">智能邮件处理系统</p>
          </div>
        </div>

        <div className="space-y-2">
          {navigationItems.map((item) => {
            const isActive = pathname === item.href;
            const Icon = item.icon;

            return (
              <Link
                key={item.name}
                href={item.href}
                className={`
                  flex items-center space-x-3 px-3 py-2 rounded-lg text-sm font-medium transition-colors
                  ${
                    isActive
                      ? 'bg-blue-50 text-blue-700 border-r-2 border-blue-700'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-gray-900'
                  }
                `}
              >
                <Icon
                  className={`h-5 w-5 ${
                    isActive ? 'text-blue-700' : 'text-gray-400'
                  }`}
                />
                <div className="flex-1">
                  <div className="font-medium">{item.name}</div>
                  <div className="text-xs text-gray-500 mt-0.5">
                    {item.description}
                  </div>
                </div>
              </Link>
            );
          })}
        </div>
      </div>

      {/* 底部状态信息 */}
      <div className="absolute bottom-0 left-0 right-0 p-4 border-t border-gray-200">
        <div className="text-xs text-gray-500 space-y-1">
          <div className="flex justify-between">
            <span>系统状态:</span>
            <span className="text-green-600 font-medium">正常</span>
          </div>
          <div className="flex justify-between">
            <span>版本:</span>
            <span>v1.0.0</span>
          </div>
        </div>
      </div>
    </nav>
  );
};

export default Navigation;
