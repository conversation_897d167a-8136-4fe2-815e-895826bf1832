{"name": "mailer-ai-agent-frontend", "version": "1.0.0", "description": "Mailer AI Agent 系统前端界面", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "format": "prettier --write .", "format:check": "prettier --check .", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "analyze": "cross-env ANALYZE=true next build", "clean": "rm -rf .next out dist"}, "dependencies": {"next": "14.0.4", "react": "18.2.0", "react-dom": "18.2.0", "typescript": "5.3.3", "@types/node": "20.10.5", "@types/react": "18.2.45", "@types/react-dom": "18.2.18", "tailwindcss": "3.3.6", "autoprefixer": "10.4.16", "postcss": "8.4.32", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0"}, "devDependencies": {"eslint": "8.56.0", "eslint-config-next": "14.0.4"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}}